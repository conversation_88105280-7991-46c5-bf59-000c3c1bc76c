package ui

import (
	"deskcrm/api/allocate"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	struStudent "deskcrm/stru/student"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// performanceService 学生课程表现数据服务
// 对应 PHP Service_Page_DeskV1_Student_PerformanceV1
type performanceService struct{}

var (
	PerformanceService performanceService
)

// GetPerformanceV1 获取学生课程表现数据
// 主要接口方法，实现与 PHP 版本完全兼容的功能
func (s performanceService) GetPerformanceV1(ctx *gin.Context, param *inputStudent.PerformanceV1Param) (rsp outputStudent.PerformanceV1Output, err error) {
	// 参数预处理和验证
	if err = s.validateAndPreprocess(ctx, param); err != nil {
		return
	}

	// 试卷数据分支处理 - 早期返回模式
	if param.Tab == consts.TAB_EXAM {
		return s.handleExamData(ctx, param)
	}

	// 初始化基础数据
	basicData, err := s.initBasicData(ctx, param)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 初始化基础数据失败: %v", err)
		return
	}

	// 根据课程类型处理数据
	processedData, err := s.processDataByType(ctx, param, basicData)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 处理数据失败: %v", err)
		return
	}

	// 设置表头配置
	headers, err := s.setTableHeaders(ctx, param, basicData)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 设置表头失败: %v", err)
		return
	}

	// 格式化表格数据
	tableData, err := s.formatTableData(ctx, param, processedData, headers)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 格式化数据失败: %v", err)
		return
	}

	// 导出处理分支
	if param.IsExport {
		return s.handleExport(ctx, param, tableData, headers)
	}

	// 构建响应结果
	rsp = outputStudent.PerformanceV1Output{
		SchemaId:    s.generateSchemaId(ctx, param),
		TableData:   tableData,
		TableHeader: headers,
	}

	return
}

// validateAndPreprocess 参数验证与预处理
func (s performanceService) validateAndPreprocess(ctx *gin.Context, param *inputStudent.PerformanceV1Param) error {
	// leadsId 获取逻辑
	if param.LeadsId == 0 {
		// 如果 leadsId 为空，尝试获取
		leadsInfo, err := allocate.NewClient().GetLeadsByBatchCourseIdUid(ctx, []int64{param.CourseId}, param.StudentUid)
		if err != nil {
			zlog.Error(ctx, "GetLeadsByBatchCourseIdUid failed: %v", err)
			return err
		}
		param.LeadsId = leadsInfo[0].LeadsId
	}

	return nil
}

// handleExamData 处理试卷数据分支
// 对应 PHP 版本的试卷数据处理逻辑
func (s performanceService) handleExamData(ctx *gin.Context, param *inputStudent.PerformanceV1Param) (outputStudent.PerformanceV1Output, error) {
	zlog.Infof(ctx, "PerformanceV1 处理试卷数据: studentUid=%d, courseId=%d", param.StudentUid, param.CourseId)

	// TODO: 调用 Service_Page_DeskV1_Student_GetExamTestList 的等价实现
	// 获取试卷测试列表数据

	// 临时返回空结构，实际实现时需要调用试卷服务
	return outputStudent.PerformanceV1Output{
		SchemaId:    "exam_schema",
		TableData:   []outputStudent.LessonTableRow{},
		TableHeader: []outputStudent.TableHeaderItem{},
	}, nil
}

// initBasicData 初始化基础数据
// 对应 PHP 版本的基础数据初始化逻辑
func (s performanceService) initBasicData(ctx *gin.Context, param *inputStudent.PerformanceV1Param) (*struStudent.BasicData, error) {
	zlog.Infof(ctx, "PerformanceV1 初始化基础数据: courseId=%d", param.CourseId)

	basicData := &struStudent.BasicData{
		CourseLessonInfos: make(map[int64]interface{}),
		LessonList:        []interface{}{},
		LessonIds:         []int64{},
		EndLessonIds:      []int64{},
		ShowLessonIds:     []int64{},
		ExamTotalNum:      make(map[string]int),
	}

	// TODO: 实现以下初始化步骤
	// 1. initCourseLessonInfos() - 获取课程章节信息
	// 2. initLessonList() - 初始化章节列表
	// 3. 判断是否LPC课程
	// 4. 初始化年级ID
	// 5. 初始化试卷总数

	return basicData, nil
}

// processDataByType 根据课程类型处理数据
// 对应 PHP 版本的双轨制数据处理逻辑
func (s performanceService) processDataByType(ctx *gin.Context, param *inputStudent.PerformanceV1Param, basicData *struStudent.BasicData) (*struStudent.ProcessedData, error) {
	zlog.Infof(ctx, "PerformanceV1 处理数据: isLpcCourse=%v", basicData.IsLpcCourse)

	processedData := &struStudent.ProcessedData{
		CommonLuData:              make(map[string]interface{}),
		DasStudentLessonInfos:     make(map[string]interface{}),
		LpcLUData:                 make(map[string]interface{}),
		LpcLessonStrengthPractice: make(map[string]interface{}),
		LessonTeacherMap:          make(map[string]interface{}),
		LessonReport:              make(map[string]interface{}),
		DeerData:                  make(map[string]interface{}),
		ScoreInfos:                make(map[string]interface{}),
		LearningPlans:             make(map[string]interface{}),
		HwBindExams:               make(map[string]interface{}),
		LessonData:                make(map[string]interface{}),
	}

	// 通用数据初始化
	if err := s.initCommonData(ctx, param, basicData, processedData); err != nil {
		return nil, err
	}

	// 根据课程类型分别处理
	if basicData.IsLpcCourse {
		// LPC课程特殊初始化
		if err := s.initLpcData(ctx, param, basicData, processedData); err != nil {
			return nil, err
		}
	} else {
		// 传统课程初始化
		if err := s.initTraditionalData(ctx, param, basicData, processedData); err != nil {
			return nil, err
		}
	}

	return processedData, nil
}

// setTableHeaders 设置表头配置
// 对应 PHP 版本的表头设置逻辑
func (s performanceService) setTableHeaders(ctx *gin.Context, param *inputStudent.PerformanceV1Param, basicData *struStudent.BasicData) ([]outputStudent.TableHeaderItem, error) {
	zlog.Infof(ctx, "PerformanceV1 设置表头配置")

	// TODO: 实现表头配置逻辑
	// 1. 获取配置模板
	// 2. 根据课程类型设置不同的表头
	// 3. 处理动态字段配置

	// 临时返回基础表头
	headers := []outputStudent.TableHeaderItem{
		{Label: "章节名称", Prop: "lessonName", Cname: "text", Width: "200", Hover: ""},
		{Label: "开始时间", Prop: "startTime", Cname: "text", Width: "150", Hover: ""},
		{Label: "到课情况", Prop: "attend", Cname: "text", Width: "100", Hover: ""},
		{Label: "预习情况", Prop: "preview", Cname: "array", Width: "120", Hover: ""},
		{Label: "作业情况", Prop: "homework", Cname: "array", Width: "120", Hover: ""},
	}

	return headers, nil
}

// formatTableData 格式化表格数据
// 对应 PHP 版本的 formatStudentLessonTableInfo 方法
func (s performanceService) formatTableData(ctx *gin.Context, param *inputStudent.PerformanceV1Param, processedData *struStudent.ProcessedData, headers []outputStudent.TableHeaderItem) ([]outputStudent.LessonTableRow, error) {
	zlog.Infof(ctx, "PerformanceV1 格式化表格数据")

	// TODO: 实现数据格式化逻辑
	// 1. 遍历章节列表
	// 2. 为每个章节构建基础数据行
	// 3. 动态方法调用处理各字段
	// 4. 方舟配置处理
	// 5. 特殊规则处理

	// 临时返回空数据
	tableData := []outputStudent.LessonTableRow{}

	return tableData, nil
}

// handleExport 处理导出功能
// 对应 PHP 版本的导出处理逻辑
func (s performanceService) handleExport(ctx *gin.Context, param *inputStudent.PerformanceV1Param, tableData []outputStudent.LessonTableRow, headers []outputStudent.TableHeaderItem) (outputStudent.PerformanceV1Output, error) {
	zlog.Infof(ctx, "PerformanceV1 处理导出: studentUid=%d", param.StudentUid)

	// TODO: 实现导出逻辑
	// 1. 调用 doExport 方法
	// 2. 生成 CSV 文件
	// 3. 返回下载链接或直接输出文件

	// 临时返回导出结果
	return outputStudent.PerformanceV1Output{
		SchemaId:    "export_schema",
		TableData:   tableData,
		TableHeader: headers,
	}, nil
}

// generateSchemaId 生成配置模式ID
// 对应 PHP 版本的 schemaId 生成逻辑
func (s performanceService) generateSchemaId(ctx *gin.Context, param *inputStudent.PerformanceV1Param) string {
	// TODO: 实现 schemaId 生成逻辑
	// 根据课程类型、配置等生成唯一标识
	return fmt.Sprintf("performance_v1_%d_%s", param.CourseId, param.Tab)
}

// initCommonData 初始化通用数据
// 对应 PHP 版本的通用数据初始化方法
func (s performanceService) initCommonData(ctx *gin.Context, param *inputStudent.PerformanceV1Param, basicData *struStudent.BasicData, processedData *struStudent.ProcessedData) error {
	zlog.Infof(ctx, "PerformanceV1 初始化通用数据")

	// TODO: 实现通用数据初始化
	// 1. initEndLessonIds() - 已结束章节ID
	// 2. initExamTotalNum() - 试卷总数
	// 3. initCommonLuData() - 通用LU数据
	// 4. initDasStudentLessonInfos() - DAS学生章节信息

	return nil
}

// initLpcData 初始化LPC课程数据
// 对应 PHP 版本的LPC课程特殊初始化方法
func (s performanceService) initLpcData(ctx *gin.Context, param *inputStudent.PerformanceV1Param, basicData *struStudent.BasicData, processedData *struStudent.ProcessedData) error {
	zlog.Infof(ctx, "PerformanceV1 初始化LPC数据")

	// TODO: 实现LPC数据初始化
	// 1. initGradeId() - 年级ID
	// 2. initLpcLUData() - LPC LU数据
	// 3. initLpcLessonStrengthPractice() - LPC巩固练习
	// 4. initLessonTeacherMap() - 教师映射
	// 5. initDimLpcLessonCommon() - LPC章节通用数据
	// 6. initLessonReport() - 课堂报告
	// 7. initDeerData() - 小鹿数据

	return nil
}

// initTraditionalData 初始化传统课程数据
// 对应 PHP 版本的传统课程初始化方法
func (s performanceService) initTraditionalData(ctx *gin.Context, param *inputStudent.PerformanceV1Param, basicData *struStudent.BasicData, processedData *struStudent.ProcessedData) error {
	zlog.Infof(ctx, "PerformanceV1 初始化传统课程数据")

	// TODO: 实现传统课程数据初始化（16个方法）
	// 1. initPreviewIsOpenInfos() - 预习开启信息
	// 2. initHomeworkIsOpenInfos() - 作业开启信息
	// 3. initAssistantLessonStudentInfos() - 辅导章节学生信息
	// 4. initScoreInfos() - 学分信息
	// 5. initLuData() - LU数据
	// 6. initLearningPlans() - 学习计划
	// 7. initHwBindExams() - 作业绑定
	// 8. initLessonData() - 章节数据
	// ... 更多方法

	return nil
}

// 方舟集成相关方法

// buildArkKeys 构建方舟规则键
// 对应迁移方案中的"组装方舟key"步骤
func (s performanceService) buildArkKeys(ctx *gin.Context, param *inputStudent.PerformanceV1Param, basicData *struStudent.BasicData, headers []outputStudent.TableHeaderItem) ([]string, error) {
	zlog.Infof(ctx, "PerformanceV1 构建方舟规则键")

	var arkKeys []string

	// TODO: 实现方舟键构建逻辑
	// 1. 根据表头配置确定需要的字段
	// 2. 构建单学生多章节维度的键
	// 3. 组装上下文参数（studentUid, lessonId等）

	// 示例键构建
	for _, header := range headers {
		if s.isArkField(header.Prop) {
			arkKey := fmt.Sprintf("%s_%d_%s", header.Prop, param.StudentUid, param.Tab)
			arkKeys = append(arkKeys, arkKey)
		}
	}

	return arkKeys, nil
}

// executeArkRules 并发执行方舟规则
// 对应迁移方案中的"通过工厂执行方舟规则"步骤
func (s performanceService) executeArkRules(ctx *gin.Context, param *inputStudent.PerformanceV1Param, arkKeys []string, basicData *struStudent.BasicData) (map[string]interface{}, error) {
	zlog.Infof(ctx, "PerformanceV1 并发执行方舟规则: keys=%v", arkKeys)

	// TODO: 实现方舟规则并发执行
	// 1. 调用方舟数据工厂
	// 2. 并发执行多个规则
	// 3. 收集执行结果

	results := make(map[string]interface{})

	// 临时返回空结果
	for _, key := range arkKeys {
		results[key] = map[string]interface{}{
			"status": "success",
			"data":   nil,
		}
	}

	return results, nil
}

// mergeArkResults 合并方舟结果
// 将方舟规则执行结果合并到表格数据中
func (s performanceService) mergeArkResults(ctx *gin.Context, tableData []outputStudent.LessonTableRow, arkResults map[string]interface{}) []outputStudent.LessonTableRow {
	zlog.Infof(ctx, "PerformanceV1 合并方舟结果")

	// TODO: 实现结果合并逻辑
	// 1. 遍历表格数据
	// 2. 根据方舟结果更新对应字段
	// 3. 处理数据映射和过滤

	return tableData
}

// isArkField 判断是否为方舟字段
// 判断字段是否需要通过方舟规则处理
func (s performanceService) isArkField(fieldName string) bool {
	// TODO: 实现方舟字段判断逻辑
	// 根据配置或规则判断字段是否需要方舟处理

	// 临时实现：假设以 "ark_" 开头的字段需要方舟处理
	return len(fieldName) > 4 && fieldName[:4] == "ark_"
}

// 动态方法调用相关方法

// getHeaderFuncMapping 获取表头字段到方法的映射
// 对应 PHP 版本的 mapHeaderFunc
func (s performanceService) getHeaderFuncMapping() map[string]string {
	return map[string]string{
		// 传统课程字段
		consts.HEADER_LESSONNAME:                              "getLessonName",
		consts.HEADER_STARTTIME:                               "getStartTime",
		consts.HEADER_PREVIEW:                                 "getPreview",
		consts.HEADER_ATTEND:                                  "getAttendData",
		consts.HEADER_PLAYBACK:                                "getPlayback",
		consts.HEADER_PLAYBACK_V1:                             "getPlaybackV1",
		consts.HEADER_LBPATTENDDURATION:                       "getLbpAttendDuration",
		consts.HEADER_LBPATTENDDURATIONOLD:                    "getLbpAttendDurationOld",
		consts.HEADER_INCLASSTEST:                             "getInclassTest",
		consts.HEADER_ORALQUESTION:                            "getOralQuestion",
		consts.HEADER_HOMEWORK:                                "getHomeworkData",
		consts.HEADER_HOMEWORK_LIKE:                           "getSimilarHomework",
		consts.HEADER_EXERCISE:                                "getExerciseColumn",
		consts.HEADER_EXERCISEALL:                             "getExerciseAll",
		consts.HEADER_LBPINTERACTEXAM:                         "getLbpInteractExam",
		consts.HEADER_MIX_PLAYBACK_INTERACT:                   "getMixPlaybackInteract",
		consts.HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS:        "getLittleKidFudaoHomeworkStatus",
		consts.HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL:         "getLittleKidFudaoHomeworkLevel",
		consts.HEADER_LITTLE_KID_FUDAO_INTERACT:               "getLittleKidFudaoInteract",
		consts.HEADER_SYNCHRONOUSPRACTICE:                     "getSynchronousPractice",
		consts.HEADER_HASCOMPOSITIONREPORT:                    "getHasCompositionReport",
		consts.HEADER_TALK:                                    "getTalk",
		consts.HEADER_SCORE:                                   "getScoreData",
		consts.HEADER_MONTHLYEXAMREPORT:                       "getMonthlyExamReport",
		consts.HEADER_IS_INCLASS_TEACHER_ROOM_ATTEND_30MINUTE: "getIsInclassTeacherRoomAttend30minute",
		consts.HEADER_IS_ATTEND_FINISH:                        "getIsAttendFinish",
		consts.HEADER_GJK_ATTEND_LESSON_LUBO:                  "getGjkAttendLessonLubo",
		consts.HEADER_GJK_COMPLETE_LESSON_LUBO:                "getGjkCompleteLessonLubo",
		consts.HEADER_GJK_LESSON_TAG:                          "getGjkLessonTag",

		// LPC课程字段
		consts.HEADER_LPC_LESSONNAME:                  "getLpcLessonName",
		consts.HEADER_LPC_TEACHERNAME:                 "getLpcTeacherName",
		consts.HEADER_LPC_ATTENDSTATUS:                "getLpcAttendStatus",
		consts.HEADER_LPC_FINISHSTATUS:                "getLpcFinishStatus",
		consts.HEADER_LPC_PLAYSTATUS:                  "getLpcPlayStatus",
		consts.HEADER_LPC_PREVIEW:                     "getLpcPreview",
		consts.HEADER_LPC_TANGTANGEXAMSTAT:            "getLpcTangtangExamStat",
		consts.HEADER_LPC_STRENGTHPRACTICE:            "getLpcStrengthPracticeData",
		consts.HEADER_LPC_LESSONREPORTURL:             "getLpcLessonReportUrl",
		consts.HEADER_DEER_ELOQUENCE_HOMEWORKLEVEL:    "getDeerEloquenceHomeworkLevel",
		consts.HEADER_DEER_PROGRAMMING_HOMEWORK_LEVEL: "getDeerProgrammingHomeworkLevel",
		consts.HEADER_LESSON_REPORT:                   "getDeerLessonReportUrl",
		consts.HEADER_LESSON_HOMEWORK:                 "getDeerLessonHomework",
		consts.HEADER_ZHIBO_LESSON_REPORT:             "getZhiboLessonReportUrl",
	}
}
