package innerapi

import (
	"deskcrm/api/achilles"
	"deskcrm/api/examcore"
	"deskcrm/components"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var ExamService examService

type examService struct {
}

// RegistTestInfo 摸底测信息
// 对应PHP版本的AssistantDesk_InterviewReferLpc::registTest返回结构
type RegistTestInfo struct {
	IsFinish   int    `json:"isFinish"`   // 是否完成 0-未完成 1-已完成
	CostTime   string `json:"costTime"`   // 耗时
	Score      int    `json:"score"`      // 分数
	FinishTime string `json:"finishTime"` // 完成时间
}

// GetCpuBindExams CPU试卷绑定情况
func (s examService) GetCpuBindExams(ctx *gin.Context, cpuIds []int64, bindType int) (map[int64]map[int]examcore.BindInfo, error) {
	if len(cpuIds) == 0 {
		return make(map[int64]map[int]examcore.BindInfo), nil
	}

	// 1. 构建绑定字符串列表
	var bindList []string
	for _, cpuId := range cpuIds {
		bindKey := fmt.Sprintf("cpu_%d:%d", cpuId, bindType)
		bindList = append(bindList, bindKey)
	}

	// 2. 兼容试卷绑定到大纲上
	enhancedBindList, outlineLessonMap, err := s.GetOutlineBindList(ctx, bindList)
	if err != nil {
		zlog.Warnf(ctx, "GetOutlineBindList failed: %v", err)
		// 如果获取outline绑定失败，继续使用原始bindList
		enhancedBindList = bindList
		outlineLessonMap = make(map[string]*achilles.ProcessedLessonInfo)
	}

	// 3. 获取试卷绑定关系
	examcoreClient := examcore.NewClient()
	examRelationList, err := examcoreClient.GetRelation(ctx, enhancedBindList)
	if err != nil {
		zlog.Warnf(ctx, "cpu试卷绑定信息获取失败: %v", err)
		return nil, err
	}

	// 4. 处理返回结果，构建以cpuId为key的数组
	cpuBindExams := make(map[int64]map[int]examcore.BindInfo)
	for _, cpuId := range cpuIds {
		bindKey := fmt.Sprintf("cpu_%d:%d", cpuId, bindType)
		if examList, exists := examRelationList[bindKey]; exists && len(examList) > 0 {
			cpuBindExams[cpuId] = examList
		}
	}

	// 5. 处理outline兼容逻辑
	if len(outlineLessonMap) > 0 {
		cpuBindExams = s.processOutlineRelation(cpuBindExams, examRelationList, outlineLessonMap)
	}

	return cpuBindExams, nil
}

// GetOutlineBindList bindList兼容outline绑定新方案
func (s examService) GetOutlineBindList(ctx *gin.Context, bindList []string) ([]string, map[string]*achilles.ProcessedLessonInfo, error) {
	// 1. 提取lesson相关的绑定，同时记录bindType信息
	var lessonIds []string
	lessonBindMap := make(map[string]string) // lessonId -> bindType的映射
	for _, bindKey := range bindList {
		if strings.Contains(bindKey, "lesson") {
			// 解析格式: lesson_123:456
			parts := strings.Split(bindKey, ":")
			if len(parts) == 2 {
				lessonPart := strings.Split(parts[0], "_")
				if len(lessonPart) == 2 {
					lessonIds = append(lessonIds, lessonPart[1])
					lessonBindMap[lessonPart[1]] = parts[1]
				}
			}
		}
	}

	// 2. 如果没有lesson绑定，直接返回原始bindList
	if len(lessonIds) == 0 {
		return bindList, make(map[string]*achilles.ProcessedLessonInfo), nil
	}

	// 3. 获取lesson信息，包含outlineId
	achillesClient := achilles.NewClient()
	lessonInfoMap, err := achillesClient.GetInfoByLessonId(ctx, lessonIds, []string{"lessonId", "outlineId"})
	if err != nil {
		zlog.Warnf(ctx, "获取lesson信息失败: %v", err)
		return bindList, make(map[string]*achilles.ProcessedLessonInfo), err
	}

	// 4. 构建lessonInfo映射，用于后续处理
	lessonInfoByIdMap := make(map[string]*achilles.ProcessedLessonInfo)
	for lessonIdStr, lessonInfo := range lessonInfoMap {
		lessonInfoByIdMap[lessonIdStr] = lessonInfo
	}

	// 5. 为每个lesson绑定添加对应的outline绑定
	for _, lessonId := range lessonIds {
		if lessonInfo, exists := lessonInfoByIdMap[lessonId]; exists {
			bindType := lessonBindMap[lessonId]
			outlineId := lessonInfo.OutlineId
			outlineBindKey := fmt.Sprintf("outline_%d:%s", outlineId, bindType)
			bindList = append(bindList, outlineBindKey)
		}
	}

	return bindList, lessonInfoByIdMap, nil
}

// processOutlineRelation 处理outline绑定兼容逻辑
func (s examService) processOutlineRelation(cpuBindExams map[int64]map[int]examcore.BindInfo, examRelationList map[string]map[int]examcore.BindInfo, outlineLessonMap map[string]*achilles.ProcessedLessonInfo) map[int64]map[int]examcore.BindInfo {
	if len(examRelationList) == 0 || len(outlineLessonMap) == 0 {
		return cpuBindExams
	}

	// 遍历examRelationList，查找outline绑定并转换为lesson绑定
	for bindKey, relation := range examRelationList {
		// 检查是否为outline绑定（格式：outline_123:456）
		if strings.Contains(bindKey, "outline") {
			// 提取outlineId：从"outline_123:456"中提取"123"
			parts := strings.Split(bindKey, ":")
			if len(parts) != 2 {
				continue
			}

			bindPart := parts[0] // "outline_123"
			bindType := parts[1] // "456"

			outlineIdParts := strings.Split(bindPart, "_")
			if len(outlineIdParts) != 2 || outlineIdParts[0] != "outline" {
				continue
			}

			outlineIdStr := outlineIdParts[1] // "123"
			outlineId, err := strconv.Atoi(outlineIdStr)
			if err != nil {
				zlog.Warnf(nil, "processOutlineRelation: 解析outlineId失败: %s, err: %v", outlineIdStr, err)
				continue
			}

			// 在outlineLessonMap中查找对应的lesson
			for _, lessonInfo := range outlineLessonMap {
				if lessonInfo.OutlineId == outlineId {
					// 构建lesson绑定key：lesson_456:789
					lessonKey := fmt.Sprintf("lesson_%d:%s", lessonInfo.LessonId, bindType)

					// 如果lesson绑定不存在，则添加（避免覆盖已有的lesson绑定）
					if _, exists := examRelationList[lessonKey]; !exists {
						examRelationList[lessonKey] = relation
						zlog.Debugf(nil, "processOutlineRelation: 添加lesson绑定 %s -> %s", bindKey, lessonKey)
					}
				}
			}
		}
	}
	return cpuBindExams
}

// RegistTest 获取摸底测信息
// 对应PHP版本的AssistantDesk_InterviewReferLpc::registTest方法
func (s examService) RegistTest(ctx *gin.Context, examId int64, studentUid int64) (*RegistTestInfo, error) {
	// 1. 参数验证
	if examId <= 0 || studentUid <= 0 {
		zlog.Warnf(ctx, "RegistTest 参数错误: examId=%d, studentUid=%d", examId, studentUid)
		return nil, fmt.Errorf("参数错误")
	}

	// 2. 初始化返回结构
	registTest := &RegistTestInfo{
		IsFinish:   0,
		CostTime:   "",
		Score:      0,
		FinishTime: "",
	}

	// 3. 获取学生试卷作答信息
	examcoreClient := examcore.NewClient()
	answerKey := fmt.Sprintf("%d_%d", examId, studentUid)
	answers, err := examcoreClient.GetAnswer(ctx, []string{answerKey})
	if err != nil {
		zlog.Warnf(ctx, "RegistTest 获取作答信息失败: examId=%d, studentUid=%d, err=%v", examId, studentUid, err)
		return registTest, nil // 返回默认值，不返回错误
	}

	// 4. 检查是否有作答记录
	answerList, exists := answers[answerKey]
	if !exists || len(answerList) == 0 {
		// 没有作答记录，返回默认的未完成状态
		return registTest, nil
	}

	// 5. 取最后一次作答记录
	lastAnswer := answerList[len(answerList)-1]

	// 6. 更新完成状态
	registTest.IsFinish = 1

	// 7. 格式化耗时
	if lastAnswer.Props.Duration > 0 {
		registTest.CostTime = components.Util.FormatRemainTime(int64(lastAnswer.Props.Duration), true)
	} else {
		registTest.CostTime = "0"
	}

	// 8. 设置分数
	registTest.Score = lastAnswer.Score

	// 9. 格式化完成时间
	if lastAnswer.CreateTime > 0 {
		registTest.FinishTime = time.Unix(int64(lastAnswer.CreateTime), 0).Format("2006-01-02 15:04:05")
	}

	return registTest, nil
}

